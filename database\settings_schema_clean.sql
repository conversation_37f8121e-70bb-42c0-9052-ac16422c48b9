-- =====================================================
-- CLEAN SETTINGS DATABASE SCHEMA FOR REVANTAD STORE
-- =====================================================
-- This version handles existing policies and tables safely
-- Run this if you got policy already exists errors
-- =====================================================

-- =====================================================
-- CLEAN UP EXISTING POLICIES (IF ANY)
-- =====================================================

-- Drop existing policies if they exist (no error if they don't exist)
DROP POLICY IF EXISTS "Store settings are viewable by authenticated users" ON store_settings;
DROP POLICY IF EXISTS "Store settings are editable by admin users" ON store_settings;
DROP POLICY IF EXISTS "Store settings are viewable by all" ON store_settings;
DROP POLICY IF EXISTS "Store settings are editable by all" ON store_settings;

DROP POLICY IF EXISTS "Users can view their own preferences" ON user_preferences;
DROP POLICY IF EXISTS "Users can update their own preferences" ON user_preferences;
DROP POLICY IF EXISTS "User preferences are viewable by all" ON user_preferences;
DROP POLICY IF EXISTS "User preferences are editable by all" ON user_preferences;

DROP POLICY IF EXISTS "System settings are viewable by authenticated users" ON system_settings;
DROP POLICY IF EXISTS "System settings are editable by admin users" ON system_settings;
DROP POLICY IF EXISTS "System settings are viewable by all" ON system_settings;
DROP POLICY IF EXISTS "System settings are editable by all" ON system_settings;

-- =====================================================
-- CREATE TABLES (IF NOT EXISTS)
-- =====================================================

-- STORE SETTINGS TABLE - Global store configuration
CREATE TABLE IF NOT EXISTS store_settings (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    store_name VARCHAR(255) NOT NULL DEFAULT 'Revantad Store',
    store_description TEXT DEFAULT 'Professional Sari-Sari Store Management System',
    store_address TEXT,
    store_phone VARCHAR(20),
    store_email VARCHAR(255),
    store_logo_url TEXT,
    store_logo_public_id TEXT, -- Cloudinary public ID
    currency VARCHAR(10) NOT NULL DEFAULT 'PHP',
    timezone VARCHAR(50) NOT NULL DEFAULT 'Asia/Manila',
    language VARCHAR(10) NOT NULL DEFAULT 'en',
    business_hours JSONB DEFAULT '{"monday": {"open": "06:00", "close": "22:00", "closed": false}, "tuesday": {"open": "06:00", "close": "22:00", "closed": false}, "wednesday": {"open": "06:00", "close": "22:00", "closed": false}, "thursday": {"open": "06:00", "close": "22:00", "closed": false}, "friday": {"open": "06:00", "close": "22:00", "closed": false}, "saturday": {"open": "06:00", "close": "22:00", "closed": false}, "sunday": {"open": "07:00", "close": "21:00", "closed": false}}',
    tax_settings JSONB DEFAULT '{"enabled": false, "rate": 0.12, "inclusive": true}',
    receipt_settings JSONB DEFAULT '{"header": "", "footer": "Thank you for your business!", "show_logo": true}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT store_settings_name_not_empty CHECK (LENGTH(TRIM(store_name)) > 0),
    CONSTRAINT store_settings_currency_valid CHECK (currency IN ('PHP', 'USD', 'EUR', 'JPY', 'SGD')),
    CONSTRAINT store_settings_language_valid CHECK (language IN ('en', 'fil', 'tl', 'es', 'zh')),
    CONSTRAINT store_settings_email_format CHECK (store_email IS NULL OR store_email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$')
);

-- USER PREFERENCES TABLE - Individual user settings and preferences
CREATE TABLE IF NOT EXISTS user_preferences (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID NOT NULL, -- Reference to auth.users (Supabase Auth)
    theme VARCHAR(20) NOT NULL DEFAULT 'light',
    notifications JSONB DEFAULT '{"email": true, "push": true, "sms": false, "lowStock": true, "newDebt": true, "payments": true, "marketing": false}',
    dashboard_settings JSONB DEFAULT '{"autoRefresh": true, "refreshInterval": 300000, "defaultView": "dashboard", "compactMode": false, "showWelcome": true}',
    display_settings JSONB DEFAULT '{"language": "en", "dateFormat": "MM/dd/yyyy", "timeFormat": "12h", "currency": "PHP", "numberFormat": "en-US"}',
    privacy_settings JSONB DEFAULT '{"shareAnalytics": false, "allowCookies": true, "dataRetention": 365}',
    accessibility_settings JSONB DEFAULT '{"highContrast": false, "largeText": false, "reduceMotion": false, "screenReader": false}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT user_preferences_theme_valid CHECK (theme IN ('light', 'dark', 'system')),
    CONSTRAINT user_preferences_user_unique UNIQUE (user_id)
);

-- SYSTEM SETTINGS TABLE - Application-wide configuration
CREATE TABLE IF NOT EXISTS system_settings (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    setting_key VARCHAR(100) NOT NULL UNIQUE,
    setting_value JSONB NOT NULL,
    setting_type VARCHAR(50) NOT NULL DEFAULT 'general',
    description TEXT,
    is_public BOOLEAN DEFAULT FALSE, -- Whether setting can be read by non-admin users
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT system_settings_key_not_empty CHECK (LENGTH(TRIM(setting_key)) > 0),
    CONSTRAINT system_settings_type_valid CHECK (setting_type IN ('general', 'security', 'performance', 'feature', 'integration'))
);

-- =====================================================
-- CREATE INDEXES (IF NOT EXISTS)
-- =====================================================

-- Store settings indexes
CREATE INDEX IF NOT EXISTS idx_store_settings_updated_at ON store_settings(updated_at DESC);

-- User preferences indexes
CREATE INDEX IF NOT EXISTS idx_user_preferences_user_id ON user_preferences(user_id);
CREATE INDEX IF NOT EXISTS idx_user_preferences_theme ON user_preferences(theme);
CREATE INDEX IF NOT EXISTS idx_user_preferences_updated_at ON user_preferences(updated_at DESC);

-- System settings indexes
CREATE INDEX IF NOT EXISTS idx_system_settings_key ON system_settings(setting_key);
CREATE INDEX IF NOT EXISTS idx_system_settings_type ON system_settings(setting_type);
CREATE INDEX IF NOT EXISTS idx_system_settings_public ON system_settings(is_public);

-- =====================================================
-- ENABLE ROW LEVEL SECURITY
-- =====================================================

-- Enable RLS on all settings tables
ALTER TABLE store_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_preferences ENABLE ROW LEVEL SECURITY;
ALTER TABLE system_settings ENABLE ROW LEVEL SECURITY;

-- =====================================================
-- CREATE NEW POLICIES (SAFE)
-- =====================================================

-- Store settings policies (permissive for development)
CREATE POLICY "revantad_store_settings_select" ON store_settings
    FOR SELECT USING (true);

CREATE POLICY "revantad_store_settings_all" ON store_settings
    FOR ALL USING (true);

-- User preferences policies (permissive for development)
CREATE POLICY "revantad_user_preferences_select" ON user_preferences
    FOR SELECT USING (true);

CREATE POLICY "revantad_user_preferences_all" ON user_preferences
    FOR ALL USING (true);

-- System settings policies (permissive for development)
CREATE POLICY "revantad_system_settings_select" ON system_settings
    FOR SELECT USING (true);

CREATE POLICY "revantad_system_settings_all" ON system_settings
    FOR ALL USING (true);

-- =====================================================
-- CREATE OR REPLACE TRIGGER FUNCTIONS
-- =====================================================

-- Update timestamps on store_settings changes
CREATE OR REPLACE FUNCTION update_store_settings_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Drop existing trigger if exists, then create new one
DROP TRIGGER IF EXISTS trigger_store_settings_updated_at ON store_settings;
CREATE TRIGGER trigger_store_settings_updated_at
    BEFORE UPDATE ON store_settings
    FOR EACH ROW
    EXECUTE FUNCTION update_store_settings_updated_at();

-- Update timestamps on user_preferences changes
CREATE OR REPLACE FUNCTION update_user_preferences_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Drop existing trigger if exists, then create new one
DROP TRIGGER IF EXISTS trigger_user_preferences_updated_at ON user_preferences;
CREATE TRIGGER trigger_user_preferences_updated_at
    BEFORE UPDATE ON user_preferences
    FOR EACH ROW
    EXECUTE FUNCTION update_user_preferences_updated_at();

-- Update timestamps on system_settings changes
CREATE OR REPLACE FUNCTION update_system_settings_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Drop existing trigger if exists, then create new one
DROP TRIGGER IF EXISTS trigger_system_settings_updated_at ON system_settings;
CREATE TRIGGER trigger_system_settings_updated_at
    BEFORE UPDATE ON system_settings
    FOR EACH ROW
    EXECUTE FUNCTION update_system_settings_updated_at();

-- =====================================================
-- INSERT DEFAULT DATA (SAFE)
-- =====================================================

-- Insert default store settings (only if no settings exist)
INSERT INTO store_settings (
    store_name,
    store_description,
    currency,
    timezone,
    language
) 
SELECT 
    'Revantad Store',
    'Professional Sari-Sari Store Management System',
    'PHP',
    'Asia/Manila',
    'en'
WHERE NOT EXISTS (SELECT 1 FROM store_settings);

-- Insert default system settings (safe upsert)
INSERT INTO system_settings (setting_key, setting_value, setting_type, description, is_public) VALUES
    ('app_version', '"2.0.0"', 'general', 'Current application version', true),
    ('maintenance_mode', 'false', 'general', 'Enable/disable maintenance mode', false),
    ('max_upload_size', '10485760', 'general', 'Maximum file upload size in bytes (10MB)', false),
    ('session_timeout', '86400', 'security', 'Session timeout in seconds (24 hours)', false),
    ('backup_enabled', 'true', 'general', 'Enable automatic database backups', false),
    ('analytics_enabled', 'true', 'feature', 'Enable analytics tracking', true),
    ('debug_mode', 'false', 'general', 'Enable debug logging', false)
ON CONFLICT (setting_key) DO NOTHING;

-- =====================================================
-- VERIFICATION QUERIES
-- =====================================================

-- Verify settings tables creation
SELECT 
    'Settings Tables Verification' as check_type,
    table_name,
    table_type
FROM information_schema.tables 
WHERE table_name IN ('store_settings', 'user_preferences', 'system_settings')
AND table_schema = 'public'
ORDER BY table_name;

-- Verify RLS is enabled
SELECT 
    'RLS Status Verification' as check_type,
    schemaname,
    tablename,
    rowsecurity as rls_enabled
FROM pg_tables 
WHERE tablename IN ('store_settings', 'user_preferences', 'system_settings')
ORDER BY tablename;

-- Verify default data insertion
SELECT 
    'Default Data Verification' as check_type,
    'store_settings' as table_name,
    COUNT(*) as record_count
FROM store_settings
UNION ALL
SELECT 
    'Default Data Verification' as check_type,
    'system_settings' as table_name,
    COUNT(*) as record_count
FROM system_settings;

-- =====================================================
-- SUCCESS MESSAGE
-- =====================================================
SELECT '🎉 CLEAN SETTINGS SCHEMA DEPLOYMENT COMPLETE ✅' as status;
SELECT 'Tables: store_settings, user_preferences, system_settings' as tables_created;
SELECT 'Policies: Clean policies created with unique names' as policies_status;
SELECT 'RLS: Enabled on all settings tables' as security_status;
SELECT 'Data: Default store and system settings inserted' as data_status;
SELECT 'Status: Ready for Settings component integration' as integration_status;
