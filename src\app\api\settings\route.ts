import { NextRequest, NextResponse } from 'next/server'

import { supabase } from '@/lib/supabase'

// Error handling wrapper
function with<PERSON>rror<PERSON><PERSON><PERSON>(handler: Function) {
  return async (request: NextRequest) => {
    try {
      return await handler(request)
    } catch (error) {
      console.error('❌ Settings API Error:', error)
      return NextResponse.json(
        { 
          error: 'Internal server error',
          details: error instanceof Error ? error.message : 'Unknown error'
        },
        { status: 500 }
      )
    }
  }
}

// GET - Fetch all settings (store settings, user preferences, system settings)
export const GET = withErrorHandler(async (request: NextRequest) => {
  const startTime = Date.now()
  console.log('🔄 Settings API GET called')

  const { searchParams } = new URL(request.url)
  const type = searchParams.get('type') // 'store', 'user', 'system', or 'all'
  const userId = searchParams.get('userId')

  try {
    const settings: any = {}

    // Fetch store settings (always included)
    if (!type || type === 'store' || type === 'all') {
      const { data: storeSettings, error: storeError } = await supabase
        .from('store_settings')
        .select('*')
        .order('updated_at', { ascending: false })
        .limit(1)
        .single()

      if (storeError) {
        if (storeError.code === 'PGRST116') {
          // No rows returned - this is OK
          settings.store = null
        } else if (storeError.code === '42P01') {
          // Table doesn't exist
          console.warn('⚠️ Store settings table does not exist. Please run the database migration.')
          settings.store = null
        } else {
          console.error('❌ Store settings fetch error:', storeError)
          settings.store = null
        }
      } else {
        settings.store = storeSettings || null
      }
    }

    // Fetch user preferences (if userId provided)
    if (userId && (!type || type === 'user' || type === 'all')) {
      const { data: userPreferences, error: userError } = await supabase
        .from('user_preferences')
        .select('*')
        .eq('user_id', userId)
        .single()

      if (userError) {
        if (userError.code === 'PGRST116') {
          // No rows returned - this is OK
          settings.user = null
        } else if (userError.code === '42P01') {
          // Table doesn't exist
          console.warn('⚠️ User preferences table does not exist. Please run the database migration.')
          settings.user = null
        } else {
          console.error('❌ User preferences fetch error:', userError)
          settings.user = null
        }
      } else {
        settings.user = userPreferences || null
      }
    }

    // Fetch system settings (public ones or all if admin)
    if (!type || type === 'system' || type === 'all') {
      const { data: systemSettings, error: systemError } = await supabase
        .from('system_settings')
        .select('*')
        .eq('is_public', true) // For now, only fetch public settings
        .order('setting_key')

      if (systemError) {
        if (systemError.code === '42P01') {
          // Table doesn't exist
          console.warn('⚠️ System settings table does not exist. Please run the database migration.')
          settings.system = []
        } else {
          console.error('❌ System settings fetch error:', systemError)
          settings.system = []
        }
      } else {
        settings.system = systemSettings || []
      }
    }

    const queryTime = Date.now() - startTime
    console.log(`✅ Settings fetched successfully in ${queryTime}ms`)

    return NextResponse.json({
      success: true,
      settings,
      queryTime: `${queryTime}ms`
    })

  } catch (error) {
    const queryTime = Date.now() - startTime
    console.error('❌ Settings fetch failed:', error)
    
    return NextResponse.json(
      { 
        error: 'Failed to fetch settings',
        details: error instanceof Error ? error.message : 'Unknown error',
        queryTime: `${queryTime}ms`
      },
      { status: 500 }
    )
  }
})

// POST - Create or update settings
export const POST = withErrorHandler(async (request: NextRequest) => {
  const startTime = Date.now()
  console.log('🔄 Settings API POST called')

  try {
    const body = await request.json()
    const { type, data, userId } = body

    console.log('📝 Settings POST request:', { type, data, userId })

    if (!type || !data) {
      return NextResponse.json(
        { error: 'Missing required fields: type and data' },
        { status: 400 }
      )
    }

    let result

    switch (type) {
      case 'store':
        console.log('🏪 Processing store settings save...')

        // Update or insert store settings
        const { data: existingStore, error: existingError } = await supabase
          .from('store_settings')
          .select('id')
          .limit(1)
          .single()

        console.log('🔍 Existing store check:', { existingStore, existingError })

        if (existingStore) {
          // Update existing store settings
          console.log('📝 Updating existing store settings...')
          const { data: updatedStore, error: updateError } = await supabase
            .from('store_settings')
            .update(data)
            .eq('id', existingStore.id)
            .select()
            .single()

          console.log('📝 Update result:', { updatedStore, updateError })
          if (updateError) throw updateError
          result = updatedStore
        } else {
          // Insert new store settings
          console.log('➕ Inserting new store settings...')
          const { data: newStore, error: insertError } = await supabase
            .from('store_settings')
            .insert(data)
            .select()
            .single()

          console.log('➕ Insert result:', { newStore, insertError })
          if (insertError) throw insertError
          result = newStore
        }
        console.log('✅ Store settings processed successfully')
        break

      case 'user':
        if (!userId) {
          return NextResponse.json(
            { error: 'userId is required for user preferences' },
            { status: 400 }
          )
        }

        // Upsert user preferences
        const { data: userPrefs, error: userError } = await supabase
          .from('user_preferences')
          .upsert({ user_id: userId, ...data })
          .select()
          .single()

        if (userError) throw userError
        result = userPrefs
        break

      case 'system':
        // Update system setting
        const { setting_key, setting_value, ...otherData } = data
        
        if (!setting_key) {
          return NextResponse.json(
            { error: 'setting_key is required for system settings' },
            { status: 400 }
          )
        }

        const { data: systemSetting, error: systemError } = await supabase
          .from('system_settings')
          .upsert({ 
            setting_key, 
            setting_value: setting_value,
            ...otherData 
          })
          .select()
          .single()

        if (systemError) throw systemError
        result = systemSetting
        break

      default:
        return NextResponse.json(
          { error: 'Invalid settings type. Must be: store, user, or system' },
          { status: 400 }
        )
    }

    const queryTime = Date.now() - startTime
    console.log(`✅ Settings ${type} saved successfully in ${queryTime}ms`)

    return NextResponse.json({
      success: true,
      data: result,
      queryTime: `${queryTime}ms`
    })

  } catch (error: any) {
    const queryTime = Date.now() - startTime
    console.error('❌ Settings save failed:', error)

    // Check if it's a table doesn't exist error
    if (error?.code === '42P01') {
      return NextResponse.json(
        {
          error: 'Settings tables do not exist',
          details: 'Please run the database migration first. Check database/settings_schema.sql',
          queryTime: `${queryTime}ms`,
          migrationRequired: true
        },
        { status: 424 } // 424 Failed Dependency
      )
    }

    return NextResponse.json(
      {
        error: 'Failed to save settings',
        details: error instanceof Error ? error.message : 'Unknown error',
        queryTime: `${queryTime}ms`
      },
      { status: 500 }
    )
  }
})

// PUT - Update specific setting
export const PUT = withErrorHandler(async (request: NextRequest) => {
  const startTime = Date.now()
  console.log('🔄 Settings API PUT called')

  try {
    const body = await request.json()
    const { type, id, data, userId } = body

    if (!type || !data) {
      return NextResponse.json(
        { error: 'Missing required fields: type and data' },
        { status: 400 }
      )
    }

    let result

    switch (type) {
      case 'store':
        if (!id) {
          return NextResponse.json(
            { error: 'id is required for store settings update' },
            { status: 400 }
          )
        }

        const { data: updatedStore, error: storeError } = await supabase
          .from('store_settings')
          .update(data)
          .eq('id', id)
          .select()
          .single()

        if (storeError) throw storeError
        result = updatedStore
        break

      case 'user':
        if (!userId) {
          return NextResponse.json(
            { error: 'userId is required for user preferences update' },
            { status: 400 }
          )
        }

        const { data: updatedUser, error: userError } = await supabase
          .from('user_preferences')
          .update(data)
          .eq('user_id', userId)
          .select()
          .single()

        if (userError) throw userError
        result = updatedUser
        break

      case 'system':
        const { setting_key } = data
        
        if (!setting_key) {
          return NextResponse.json(
            { error: 'setting_key is required for system settings update' },
            { status: 400 }
          )
        }

        const { data: updatedSystem, error: systemError } = await supabase
          .from('system_settings')
          .update(data)
          .eq('setting_key', setting_key)
          .select()
          .single()

        if (systemError) throw systemError
        result = updatedSystem
        break

      default:
        return NextResponse.json(
          { error: 'Invalid settings type. Must be: store, user, or system' },
          { status: 400 }
        )
    }

    const queryTime = Date.now() - startTime
    console.log(`✅ Settings ${type} updated successfully in ${queryTime}ms`)

    return NextResponse.json({
      success: true,
      data: result,
      queryTime: `${queryTime}ms`
    })

  } catch (error) {
    const queryTime = Date.now() - startTime
    console.error('❌ Settings update failed:', error)
    
    return NextResponse.json(
      { 
        error: 'Failed to update settings',
        details: error instanceof Error ? error.message : 'Unknown error',
        queryTime: `${queryTime}ms`
      },
      { status: 500 }
    )
  }
})
