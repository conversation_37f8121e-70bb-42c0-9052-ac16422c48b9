-- =====================================================
-- QUICK FIX FOR SETTINGS DATABASE - SAFE TO RUN MULTIPLE TIMES
-- =====================================================
-- This will only create what's missing and insert default data
-- =====================================================

-- Insert default store settings (only if no settings exist)
INSERT INTO store_settings (
    store_name,
    store_description,
    currency,
    timezone,
    language
) 
SELECT 
    'Revantad Store',
    'Professional Sari-Sari Store Management System',
    'PHP',
    'Asia/Manila',
    'en'
WHERE NOT EXISTS (SELECT 1 FROM store_settings);

-- Insert default system settings (safe upsert)
INSERT INTO system_settings (setting_key, setting_value, setting_type, description, is_public) VALUES
    ('app_version', '"2.0.0"', 'general', 'Current application version', true),
    ('maintenance_mode', 'false', 'general', 'Enable/disable maintenance mode', false),
    ('max_upload_size', '10485760', 'general', 'Maximum file upload size in bytes (10MB)', false),
    ('session_timeout', '86400', 'security', 'Session timeout in seconds (24 hours)', false),
    ('backup_enabled', 'true', 'general', 'Enable automatic database backups', false),
    ('analytics_enabled', 'true', 'feature', 'Enable analytics tracking', true),
    ('debug_mode', 'false', 'general', 'Enable debug logging', false)
ON CONFLICT (setting_key) DO NOTHING;

-- =====================================================
-- VERIFICATION QUERIES
-- =====================================================

-- Check if we have data now
SELECT 
    'store_settings' as table_name,
    COUNT(*) as record_count,
    store_name
FROM store_settings
GROUP BY store_name;

SELECT 
    'system_settings' as table_name,
    COUNT(*) as record_count
FROM system_settings;

-- Success message
SELECT '✅ QUICK FIX COMPLETE - Settings should work now!' as status;
